<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Enums\CompanyType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Organization>
 */
class OrganizationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'description' => fake()->sentence(),
            'is_active' => true,
            'is_suspended' => false,
            'default_flow_id' => null,
            'email' => fake()->unique()->safeEmail(),
            'cpf_cnpj' => fake()->numerify('##############'),
            'company_type' => fake()->randomElement(CompanyType::cases()),
            'phone' => fake()->phoneNumber(),
            'mobile_phone' => fake()->phoneNumber(),
            'address' => fake()->streetAddress(),
            'address_number' => fake()->buildingNumber(),
            'complement' => fake()->optional()->secondaryAddress(),
            'province' => fake()->citySuffix(),
            'city' => fake()->city(),
            'state' => fake()->stateAbbr(),
            'postal_code' => fake()->postcode(),
            'birth_date' => fake()->optional()->date(),
            'asaas_account_id' => fake()->optional()->uuid(),
            'asaas_api_key' => fake()->optional()->sha256(),
            'asaas_wallet_id' => fake()->optional()->uuid(),
            'asaas_environment' => fake()->randomElement(['sandbox', 'production']),
            'asaas_subscription_id' => fake()->optional()->uuid(),
            'subscription_status' => fake()->optional()->randomElement(['ACTIVE', 'INACTIVE', 'PENDING', 'CANCELLED']),
            'subscription_value' => fake()->optional()->randomFloat(2, 10, 1000),
            'subscription_due_date' => fake()->optional()->date(),
            'subscription_started_at' => fake()->optional()->dateTime(),
            'subscription_expires_at' => fake()->optional()->dateTime(),
            'is_courtesy' => fake()->boolean(20), // 20% chance of being courtesy
            'courtesy_expires_at' => fake()->optional()->date(),
            'courtesy_reason' => fake()->optional()->sentence(),
            'monthly_revenue' => fake()->optional()->randomFloat(2, 1000, 100000),
            'income_value' => fake()->optional()->randomFloat(2, 1000, 50000),
        ];
    }

    /**
     * Indicate that the organization is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspended' => true,
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the organization is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Organization with basic contact info for ASAAS
     */
    public function withContactInfo(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->company(),
            'description' => fake()->sentence(),
        ]);
    }
}
