# EPIC: Sistema de Webhook Multi-Organizacional WhatsApp

## Visão Geral da Epic

**Objetivo:** Implementar sistema de webhook unificado para múltiplas organizações no WhatsApp, permitindo que cada organização tenha seu próprio token de verificação enquanto mantém múltiplos números por organização.

**Valor de Negócio:** Escalabilidade para suportar milhares de organizações com webhook único, melhor isolamento de dados e configuração flexível por cliente.

**Duração Estimada:** 5-7 dias úteis
**Complexidade:** Média
**Prioridade:** Alta

---

## Ticket 1: Resolver Conflitos de Rotas Webhook

### Descrição
Atualmente existem rotas duplicadas para o webhook WhatsApp que causam conflitos. Precisamos consolidar em um único controller e endpoint.

### Acceptance Criteria
- [ ] Remover rotas duplicadas em `routes/api.php`
- [ ] Manter apenas `WhatsAppWebhookController` como controller principal
- [ ] Endpoint único: `GET/POST /api/whatsapp/webhook`
- [ ] Remover `WebhookController` da pasta Meta/WhatsApp
- [ ] Testes existentes continuam passando
- [ ] Webhook atual continua funcionando sem quebras

### Tarefas Técnicas
1. Analisar diferenças entre `WhatsAppWebhookController` e `WebhookController`
2. Consolidar lógica no `WhatsAppWebhookController`
3. Remover rotas duplicadas
4. Atualizar testes se necessário
5. Validar funcionamento com webhook atual

### Definição de Pronto
- Apenas um endpoint de webhook ativo
- Testes passando
- Documentação atualizada
- Webhook funcionando em ambiente de desenvolvimento

---

## Ticket 2: Centralizar Configurações WhatsApp

### Descrição
Mover todas as configurações WhatsApp fragmentadas para um local centralizado em `config/services.php` e organizar variáveis de ambiente.

### Acceptance Criteria
- [ ] Criar seção `meta.whatsapp` em `config/services.php`
- [ ] Migrar configs de `config/whatsapp.php`
- [ ] Atualizar variáveis de ambiente no `.env.example`
- [ ] Manter compatibilidade com código existente
- [ ] Documentar novas configurações

### Tarefas Técnicas
1. Criar estrutura em `config/services.php`
2. Migrar configurações existentes
3. Atualizar `.env.example` e `.env.docs`
4. Verificar se código existente continua funcionando
5. Criar documentação de configuração

### Configurações a Centralizar
```php
'meta' => [
    'whatsapp' => [
        'webhook_verify_token' => env('META_WHATSAPP_WEBHOOK_VERIFY_TOKEN'),
        'webhook_secret' => env('META_WHATSAPP_WEBHOOK_SECRET'),
        'api_base_url' => env('META_WHATSAPP_API_BASE_URL', 'https://graph.facebook.com/v22.0'),
    ]
]
```

### Definição de Pronto
- Configurações centralizadas
- Código existente funcionando
- Documentação atualizada
- Variáveis de ambiente organizadas

---

## Ticket 3: Criar Sistema de Logs de Webhook

### Descrição
Implementar sistema de auditoria completo para todos os eventos de webhook, permitindo rastreamento e debug por organização.

### Acceptance Criteria
- [ ] Migration para tabela `whatsapp_webhook_logs`
- [ ] Model `WhatsAppWebhookLog` com relacionamentos
- [ ] Logging automático de todos os eventos de webhook
- [ ] Diferentes tipos de evento (message, status, other)
- [ ] Status de processamento (pending, success, failed)
- [ ] Payload completo armazenado em JSON

### Tarefas Técnicas
1. Criar migration para `whatsapp_webhook_logs`
2. Criar model `WhatsAppWebhookLog`
3. Implementar logging no controller de webhook
4. Criar factory para testes
5. Implementar testes unitários

### Estrutura da Tabela
```sql
CREATE TABLE whatsapp_webhook_logs (
    id BIGINT PRIMARY KEY,
    organization_id BIGINT NULL,
    phone_number_id VARCHAR(255) NULL,
    event_type ENUM('message', 'status', 'other'),
    webhook_payload JSON,
    processed_at TIMESTAMP NULL,
    processing_status ENUM('pending', 'success', 'failed'),
    error_message TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX(organization_id),
    INDEX(phone_number_id),
    INDEX(event_type),
    INDEX(processing_status)
);
```

### Definição de Pronto
- Tabela criada e migrada
- Model funcionando
- Logging implementado
- Testes passando
- Documentação da estrutura

---

## Ticket 4: Adicionar Token de Verificação por Organização

### Descrição
Adicionar campo `whatsapp_webhook_verify_token` na tabela organizations para permitir tokens de verificação específicos por organização.

### Acceptance Criteria
- [ ] Migration para adicionar campo em `organizations`
- [ ] Campo nullable para manter compatibilidade
- [ ] Atualizar model `Organization` com novo campo
- [ ] Atualizar factory de Organization
- [ ] Manter funcionamento atual como fallback

### Tarefas Técnicas
1. Criar migration para adicionar campo
2. Atualizar model Organization
3. Atualizar factory Organization
4. Atualizar testes existentes
5. Documentar novo campo

### Migration
```sql
ALTER TABLE organizations 
ADD whatsapp_webhook_verify_token VARCHAR(255) NULL 
AFTER whatsapp_webhook_verify_token;

CREATE INDEX idx_organizations_webhook_token 
ON organizations(whatsapp_webhook_verify_token);
```

### Definição de Pronto
- Campo adicionado na tabela
- Model atualizado
- Factory atualizada
- Testes passando
- Documentação atualizada

---

## Ticket 5: Implementar Identificação de Organização

### Descrição
Criar classe `OrganizationIdentifier` para mapear `phone_number_id` recebido no webhook para a organização correspondente através do PhoneNumber.

### Acceptance Criteria
- [ ] Classe `OrganizationIdentifier` criada
- [ ] Método para buscar organização por `phone_number_id`
- [ ] Retorna tanto Organization quanto PhoneNumber
- [ ] Tratamento de casos onde não encontra
- [ ] Performance otimizada com eager loading
- [ ] Testes unitários completos

### Tarefas Técnicas
1. Criar classe `App\Services\Meta\WhatsApp\OrganizationIdentifier`
2. Implementar método `identifyByPhoneNumberId`
3. Otimizar query com relacionamentos
4. Implementar tratamento de erros
5. Criar testes unitários abrangentes

### Implementação
```php
class OrganizationIdentifier
{
    public function identifyByPhoneNumberId(string $phoneNumberId): ?array
    {
        $phoneNumber = PhoneNumber::where('whatsapp_phone_number_id', $phoneNumberId)
                                 ->where('is_active', true)
                                 ->with(['organization' => function($query) {
                                     $query->where('is_active', true)
                                           ->where('is_suspended', false);
                                 }])
                                 ->first();

        if (!$phoneNumber || !$phoneNumber->organization) {
            return null;
        }

        return [
            'organization' => $phoneNumber->organization,
            'phone_number' => $phoneNumber
        ];
    }
}
```

### Definição de Pronto
- Classe implementada
- Busca funcionando corretamente
- Performance otimizada
- Testes unitários passando
- Documentação da classe

---

## Ticket 6: Implementar Verificação de Token por Organização

### Descrição
Atualizar método `verify` do webhook para suportar tokens específicos por organização, mantendo fallback para token global.

### Acceptance Criteria
- [ ] Verificação por token de organização implementada
- [ ] Fallback para token global mantido
- [ ] Logs de verificação implementados
- [ ] Resposta correta para Meta (challenge)
- [ ] Tratamento de tokens inválidos
- [ ] Testes para todos os cenários

### Tarefas Técnicas
1. Atualizar método `verify` em `WhatsAppWebhookController`
2. Implementar busca por token de organização
3. Manter fallback para token global
4. Adicionar logging detalhado
5. Criar testes para todos os cenários

### Implementação
```php
public function verify(Request $request): JsonResponse
{
    $mode = $request->query('hub_mode');
    $token = $request->query('hub_verify_token');
    $challenge = $request->query('hub_challenge');

    if ($mode !== 'subscribe') {
        return response()->json(['error' => 'Invalid mode'], 400);
    }

    // Prioridade 1: Buscar organização por token específico
    $organization = Organization::where('whatsapp_webhook_verify_token', $token)
                                ->where('is_active', true)
                                ->where('is_suspended', false)
                                ->first();

    if ($organization) {
        Log::info('WhatsApp webhook verified for organization', [
            'organization_id' => $organization->id,
            'token' => substr($token, 0, 8) . '...'
        ]);
        return response()->json((int) $challenge);
    }

    // Prioridade 2: Fallback para token global
    if ($token === config('services.meta.whatsapp.webhook_verify_token')) {
        Log::info('WhatsApp webhook verified with global token');
        return response()->json((int) $challenge);
    }

    Log::warning('WhatsApp webhook verification failed', [
        'token' => substr($token, 0, 8) . '...',
        'mode' => $mode
    ]);

    return response()->json(['error' => 'Forbidden'], 403);
}
```

### Definição de Pronto
- Verificação por organização funcionando
- Fallback global mantido
- Logs implementados
- Testes cobrindo todos os cenários
- Documentação atualizada

---

## Ticket 7: Implementar Processamento Unificado de Webhook

### Descrição
Atualizar método `handle` do webhook para usar o novo sistema de identificação de organização e rotear eventos por tipo (messages vs statuses).

### Acceptance Criteria
- [ ] Identificação de organização por `phone_number_id`
- [ ] Roteamento por tipo de evento (messages, statuses)
- [ ] Logging completo de todos os eventos
- [ ] Manter compatibilidade com ChatBot existente
- [ ] Tratamento de erros robusto
- [ ] Validação de payload do Meta

### Tarefas Técnicas
1. Atualizar método `handle` em `WhatsAppWebhookController`
2. Integrar `OrganizationIdentifier`
3. Implementar roteamento por tipo de evento
4. Adicionar logging com `WhatsAppWebhookLog`
5. Manter compatibilidade com sistema atual
6. Criar testes de integração

### Implementação
```php
public function handle(Request $request): JsonResponse
{
    try {
        $webhookData = $request->all();

        // Validar estrutura básica
        if (!$this->isValidWebhookData($webhookData)) {
            return response()->json(['error' => 'Invalid webhook data'], 400);
        }

        $results = [];

        foreach ($webhookData['entry'] ?? [] as $entry) {
            foreach ($entry['changes'] ?? [] as $change) {
                if ($change['field'] === 'messages') {
                    $result = $this->processMessageChange($change['value']);
                    if ($result) {
                        $results[] = $result;
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'processed' => count($results),
            'results' => $results
        ]);

    } catch (\Exception $e) {
        Log::error('WhatsApp webhook processing error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'data' => $request->all()
        ]);

        return response()->json([
            'status' => 'error',
            'message' => 'Internal server error'
        ], 500);
    }
}

private function processMessageChange(array $changeValue): ?array
{
    // Extrair phone_number_id
    $phoneNumberId = $changeValue['metadata']['phone_number_id'] ?? null;

    if (!$phoneNumberId) {
        Log::warning('Missing phone_number_id in webhook');
        return null;
    }

    // Identificar organização
    $identifier = app(OrganizationIdentifier::class);
    $identificationResult = $identifier->identifyByPhoneNumberId($phoneNumberId);

    if (!$identificationResult) {
        Log::warning('Organization not found for phone_number_id', [
            'phone_number_id' => $phoneNumberId
        ]);
        return null;
    }

    $organization = $identificationResult['organization'];
    $phoneNumber = $identificationResult['phone_number'];

    // Log do evento
    WhatsAppWebhookLog::create([
        'organization_id' => $organization->id,
        'phone_number_id' => $phoneNumberId,
        'event_type' => isset($changeValue['messages']) ? 'message' : 'status',
        'webhook_payload' => $changeValue,
        'processing_status' => 'pending'
    ]);

    // Rotear por tipo de evento
    if (isset($changeValue['messages'])) {
        return $this->processIncomingMessages($changeValue, $organization, $phoneNumber);
    } elseif (isset($changeValue['statuses'])) {
        return $this->processMessageStatuses($changeValue, $organization, $phoneNumber);
    }

    return null;
}
```

### Definição de Pronto
- Identificação de organização funcionando
- Roteamento por tipo implementado
- Logging completo
- Compatibilidade mantida
- Testes de integração passando

---

## Ticket 8: Implementar Validação de Assinatura

### Descrição
Adicionar validação de assinatura X-Hub-Signature-256 do Meta para garantir que os webhooks são legítimos.

### Acceptance Criteria
- [ ] Validação de assinatura X-Hub-Signature-256
- [ ] Configuração de webhook secret
- [ ] Rejeição de webhooks com assinatura inválida
- [ ] Logs de tentativas de webhook inválidas
- [ ] Testes para validação de assinatura

### Tarefas Técnicas
1. Implementar método de validação de assinatura
2. Adicionar middleware ou validação no controller
3. Configurar webhook secret
4. Adicionar logging de segurança
5. Criar testes para validação

### Implementação
```php
private function validateWebhookSignature(Request $request): bool
{
    $signature = $request->header('X-Hub-Signature-256');
    $payload = $request->getContent();
    $secret = config('services.meta.whatsapp.webhook_secret');

    if (!$signature || !$secret) {
        return false;
    }

    $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

    return hash_equals($expectedSignature, $signature);
}
```

### Definição de Pronto
- Validação de assinatura implementada
- Webhooks inválidos rejeitados
- Logs de segurança funcionando
- Testes passando
- Documentação de segurança

---

## Ticket 9: Criar Testes Abrangentes

### Descrição
Implementar suite completa de testes unitários e de integração para todo o sistema de webhook multi-organizacional.

### Acceptance Criteria
- [ ] Testes unitários para `OrganizationIdentifier`
- [ ] Testes de integração para webhook completo
- [ ] Testes para verificação de token por organização
- [ ] Testes para roteamento de eventos
- [ ] Testes para logging de webhook
- [ ] Testes para validação de assinatura
- [ ] Cobertura de testes > 90%

### Tarefas Técnicas
1. Criar testes para `OrganizationIdentifier`
2. Criar testes de webhook end-to-end
3. Criar testes para verificação de token
4. Criar testes para processamento de eventos
5. Criar testes para logging
6. Verificar cobertura de testes

### Cenários de Teste
- Webhook com token de organização válido
- Webhook com token global (fallback)
- Webhook com token inválido
- Webhook com phone_number_id válido
- Webhook com phone_number_id inexistente
- Webhook com payload de mensagem
- Webhook com payload de status
- Webhook com assinatura válida/inválida
- Webhook com organização inativa/suspensa

### Definição de Pronto
- Todos os testes passando
- Cobertura adequada
- Testes de regressão funcionando
- Documentação de testes
- CI/CD configurado

---

## Ticket 10: Documentação e Configuração

### Descrição
Criar documentação completa para configuração e uso do sistema de webhook multi-organizacional.

### Acceptance Criteria
- [ ] Documentação de configuração no Meta Business
- [ ] Guia de configuração por organização
- [ ] Documentação de troubleshooting
- [ ] Exemplos de payloads de webhook
- [ ] Documentação de API interna
- [ ] Guia de migração (se necessário)

### Tarefas Técnicas
1. Documentar configuração no Meta Business
2. Criar guia de configuração por organização
3. Documentar troubleshooting comum
4. Criar exemplos de uso
5. Documentar APIs internas
6. Criar guia de migração

### Documentação Necessária
- Como configurar webhook no Meta Business
- Como definir token por organização
- Como testar webhook
- Como debugar problemas
- Estrutura de logs
- APIs de consulta de logs

### Definição de Pronto
- Documentação completa
- Guias funcionais
- Exemplos testados
- Troubleshooting documentado
- Migração documentada (se aplicável)

---

## Resumo da Epic

### Ordem de Implementação
1. **Ticket 1-2**: Preparação (rotas + configs)
2. **Ticket 3-4**: Infraestrutura (logs + token)
3. **Ticket 5-7**: Core (identificação + processamento)
4. **Ticket 8**: Segurança (validação)
5. **Ticket 9-10**: Qualidade (testes + docs)

### Dependências
- Ticket 5 depende de Ticket 4
- Ticket 6-7 dependem de Ticket 5
- Ticket 8 pode ser paralelo a 6-7
- Ticket 9 depende de todos os anteriores
- Ticket 10 pode ser paralelo aos demais

### Riscos e Mitigações
- **Risco**: Quebrar webhook atual
- **Mitigação**: Manter compatibilidade total
- **Risco**: Performance da busca
- **Mitigação**: Índices otimizados
- **Risco**: Configuração complexa
- **Mitigação**: Documentação detalhada
