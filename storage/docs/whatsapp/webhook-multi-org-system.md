# Sistema de Webhook Multi-Organizacional WhatsApp

## Visão Geral

Este documento define a arquitetura e implementação do sistema de webhook unificado para múltiplas organizações usando a Meta WhatsApp Cloud API.

## Contexto Atual

### Problema Identificado
- Atualmente temos dois controllers de webhook (`WebhookController` e `WhatsAppWebhookController`)
- Sistema não está preparado para multi-tenant adequadamente
- Falta mapeamento entre `phone_number_id` e organizações
- Token de verificação único para todas as organizações

### Meta WhatsApp Cloud API - Como Funciona
- **Um webhook único** por aplicação Meta (não por organização)
- Payload sempre contém `phone_number_id` e `whatsapp_business_account_id`
- Diferentes tipos de eventos no mesmo endpoint:
  - `messages`: Mensagens recebidas de clientes
  - `statuses`: Status de mensagens enviadas (entregue, lida, falhou)
  - Outros eventos futuros

## Arquitetura Proposta

### 1. Webhook Unificado

```
Meta WhatsApp Cloud API
        ↓
Webhook Único (/api/whatsapp/webhook)
        ↓
Identificação da Organização (phone_number_id)
        ↓
Roteamento por Tipo de Evento
        ↓
┌─────────────────┬─────────────────┐
│   ChatBot       │   Campanhas     │
│  (messages)     │  (statuses)     │
└─────────────────┴─────────────────┘
```

### 2. Estrutura de Dados

#### Tabela: organizations (APENAS 1 campo novo)
```sql
-- Campos existentes +
whatsapp_webhook_verify_token VARCHAR(255) NULL, -- Token de verificação por organização
```

#### Tabela: phone_numbers (PERMANECE COMO ESTÁ)
```sql
-- Todos os dados WhatsApp permanecem aqui (onde já estão e onde fazem sentido)
whatsapp_phone_number_id VARCHAR(255) NULL,     -- ID do número no Meta
whatsapp_business_id VARCHAR(255) NULL,         -- ID da conta business
whatsapp_access_token LONGTEXT NULL,            -- Token de acesso do número
-- + todos os outros campos existentes
```

#### Tabela: whatsapp_webhook_logs (NOVA)
```sql
id BIGINT PRIMARY KEY,
organization_id BIGINT,
phone_number_id VARCHAR(255),
business_account_id VARCHAR(255),
event_type ENUM('message', 'status', 'other'),
webhook_payload JSON,
processed_at TIMESTAMP,
processing_status ENUM('pending', 'success', 'failed'),
error_message TEXT NULL,
created_at TIMESTAMP,
updated_at TIMESTAMP
```

### 3. Fluxo de Processamento

#### 3.1 Verificação do Webhook (GET)
```php
// Único token de verificação global
if ($mode === 'subscribe' && $token === config('services.meta.webhook_verify_token')) {
    return response()->json((int) $challenge);
}
```

#### 3.2 Processamento de Eventos (POST)
```php
1. Receber payload do webhook
2. Extrair phone_number_id do payload
3. Buscar organização pelo phone_number_id
4. Validar se organização está ativa
5. Determinar tipo de evento (messages vs statuses)
6. Rotear para módulo apropriado
7. Registrar log do processamento
```

## Implementação Detalhada

### 1. Controller Unificado

```php
class WhatsAppWebhookController extends Controller
{
    public function verify(Request $request): JsonResponse
    {
        // Verificação única global
    }

    public function handle(Request $request): JsonResponse
    {
        // 1. Validar estrutura do payload
        // 2. Processar cada entry
        // 3. Para cada change, identificar organização
        // 4. Rotear por tipo de evento
    }

    private function identifyOrganization(array $changeValue): ?Organization
    {
        // Buscar por phone_number_id ou business_account_id
    }

    private function routeEvent(string $eventType, array $data, Organization $org): array
    {
        // Rotear para ChatBot ou Campanhas
    }
}
```

### 2. Use Cases

#### ProcessWebhookMessage (ChatBot)
```php
class ProcessWebhookMessage
{
    public function perform(array $webhookData, Organization $organization): array
    {
        // Processar mensagens recebidas
        // Usar access_token da organização
    }
}
```

#### ProcessWebhookStatus (Campanhas)
```php
class ProcessWebhookStatus
{
    public function perform(array $webhookData, Organization $organization): array
    {
        // Processar status de mensagens enviadas
        // Atualizar logs de campanha
    }
}
```

### 3. Identificação de Organização

```php
class OrganizationIdentifier
{
    public function identifyByPhoneNumberId(string $phoneNumberId): ?Organization
    {
        return Organization::where('whatsapp_phone_number_id', $phoneNumberId)
                          ->where('is_active', true)
                          ->first();
    }

    public function identifyByBusinessAccountId(string $businessAccountId): ?Organization
    {
        return Organization::where('whatsapp_business_account_id', $businessAccountId)
                          ->where('is_active', true)
                          ->first();
    }
}
```

## Estrutura de Payload

### Mensagem Recebida
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "**********"
            },
            "contacts": [
              {
                "profile": {"name": "João Silva"},
                "wa_id": "*************"
              }
            ],
            "messages": [
              {
                "from": "*************",
                "id": "wamid.HBgLM...",
                "timestamp": "**********",
                "text": {"body": "Oi, tudo bem?"},
                "type": "text"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

### Status de Mensagem
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "**********"
            },
            "statuses": [
              {
                "id": "wamid.HBgLM...",
                "status": "delivered",
                "timestamp": "**********",
                "recipient_id": "*************"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

## Segurança

### 1. Validação de Assinatura
```php
// Validar X-Hub-Signature-256 do Meta
private function validateWebhookSignature(Request $request): bool
{
    $signature = $request->header('X-Hub-Signature-256');
    $payload = $request->getContent();
    $secret = config('services.meta.webhook_secret');
    
    $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
    
    return hash_equals($expectedSignature, $signature);
}
```

### 2. Rate Limiting
```php
// Aplicar rate limiting por organização
Route::middleware(['throttle:whatsapp-webhook'])->group(function () {
    Route::post('/webhook', [WhatsAppWebhookController::class, 'handle']);
});
```

## Análise Detalhada da Estrutura Atual

### Estado Atual do Sistema

#### 1. Modelo PhoneNumber - Análise Completa
```sql
-- Estrutura atual da tabela phone_numbers
CREATE TABLE phone_numbers (
    id BIGINT PRIMARY KEY,
    organization_id BIGINT NULL INDEX,
    user_id BIGINT NULL INDEX,
    client_id BIGINT NULL INDEX,
    flow_id BIGINT NULL INDEX,
    phone_number VARCHAR(255) NULL,
    name VARCHAR(255) NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT true,
    whatsapp_phone_number_id VARCHAR(255) NULL,  -- CHAVE PARA WEBHOOK
    whatsapp_business_id VARCHAR(255) NULL,      -- CHAVE PARA WEBHOOK
    whatsapp_access_token LONGTEXT NULL,         -- TOKEN POR NÚMERO
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### 2. Relacionamentos Atuais
- **PhoneNumber → Organization**: `belongsTo` (organization_id)
- **PhoneNumber → User**: `belongsTo` (user_id)
- **PhoneNumber → Client**: `belongsTo` (client_id)
- **PhoneNumber → Flow**: `belongsTo` (flow_id)
- **Campaign → PhoneNumber**: `belongsTo` (phone_number_id)
- **Template → PhoneNumber**: `belongsTo` (phone_number_id)

#### 3. Uso Atual do PhoneNumber
```php
// WhatsAppService.php - Construtor aceita PhoneNumber
public function __construct(?PhoneNumber $phoneNumber = null)
{
    $this->phoneNumberId = $phoneNumber?->whatsapp_phone_number_id ?? config('whatsapp.phone_number_id');
    $this->businessId = $phoneNumber?->whatsapp_business_id ?? config('whatsapp.business_id');
    $this->token = $phoneNumber?->whatsapp_access_token ?? config('whatsapp.access_token');
}

// FindOrCreateClient.php - Busca organização via PhoneNumber
protected function getOrganizationIdFromPhoneNumber(array $messageData): int
{
    $phoneNumberId = $messageData['phone_number_id'];
    // TODO: Implementar lookup PhoneNumber → Organization
    return 1; // Hardcoded atualmente
}
```

### Problemas Identificados

#### 1. Rotas Duplicadas - CONFLITO CRÍTICO
```php
// routes/api.php - DUAS ROTAS PARA O MESMO ENDPOINT
Route::get("/whatsapp/webhook", [WhatsAppWebhookController::class, "verify"]);
Route::post("/whatsapp/webhook", [WhatsAppWebhookController::class, "receiveMessage"]);

Route::prefix('whatsapp/webhook')->group(function () {
    Route::get('/', [WebhookController::class, 'verify']);    // CONFLITO
    Route::post('/', [WebhookController::class, 'handle']);   // CONFLITO
});
```

#### 2. Arquitetura Multi-Tenant Correta
- **Realidade**: Uma organização TEM múltiplos PhoneNumbers (1:N) - CORRETO
- **Necessidade**: Webhook precisa identificar organização por phone_number_id
- **Solução**: phone_number_id → PhoneNumber → Organization (JOIN natural)
- **Apenas adicionar**: Token de verificação por organização

#### 3. Configurações Fragmentadas
- `config/whatsapp.php` - Configurações básicas (fallback)
- `config/services.php` - Sem seção Meta/WhatsApp
- `.env` - Tokens globais como fallback
- **Solução**: Centralizar configs + token de verificação por organização

#### 4. Controllers Conflitantes
- `WhatsAppWebhookController::receiveMessage()` - Foca em ChatBot
- `WebhookController::handle()` - Usa ProcessWebhookEntry
- **Problema**: Lógica duplicada, inconsistente, confusa

### Questões Críticas para Migração

#### 1. PhoneNumber vs Organization - Análise de Impacto

**Cenário Atual:**
- Uma Organization pode ter múltiplos PhoneNumbers
- Cada PhoneNumber tem seus próprios tokens WhatsApp
- Campanhas e Templates são vinculados a PhoneNumbers específicos

**Cenário Proposto:**
- Uma Organization tem campos WhatsApp diretos
- Simplifica identificação no webhook
- **QUESTÃO**: O que fazer com múltiplos números por organização?

#### 2. Estratégias de Migração - 3 Opções

**Opção A: Migração Completa (BREAKING CHANGE)**
```sql
-- Mover campos de phone_numbers para organizations
ALTER TABLE organizations ADD whatsapp_phone_number_id VARCHAR(255);
ALTER TABLE organizations ADD whatsapp_business_account_id VARCHAR(255);
ALTER TABLE organizations ADD whatsapp_access_token LONGTEXT;

-- Deprecar phone_numbers para WhatsApp
-- Manter apenas para flows e outras funcionalidades
```

**Opção B: Híbrida (COMPATIBILIDADE)**
```sql
-- Adicionar campos em organizations
-- Manter phone_numbers como está
-- Webhook usa organizations primeiro, fallback para phone_numbers
```

**Opção C: Manter PhoneNumber (MÍNIMO IMPACTO)**
```sql
-- Não mexer na estrutura atual
-- Melhorar apenas o sistema de identificação
-- Criar cache/índice para phone_number_id → organization_id
```

#### 3. Impacto nos Relacionamentos

**Campanhas e Templates:**
```php
// ATUAL
Campaign::belongsTo(PhoneNumber::class)
Template::belongsTo(PhoneNumber::class)

// PROPOSTO (Opção A)
Campaign::belongsTo(Organization::class) // Via organization_id
Template::belongsTo(Organization::class) // Via organization_id

// PROBLEMA: E se organização tem múltiplos números?
```

**WhatsAppService:**
```php
// ATUAL
new WhatsAppService($phoneNumber) // Recebe PhoneNumber

// PROPOSTO
new WhatsAppService($organization) // Recebe Organization
// MAS: Como escolher qual número usar se há múltiplos?
```

### Dependências Atuais - Mapeamento Completo

#### Models e Relacionamentos
- `Organization` ← `PhoneNumber` (organization_id)
- `PhoneNumber` ← `Campaign` (phone_number_id)
- `PhoneNumber` ← `Template` (phone_number_id)
- `PhoneNumber` → `Flow` (flow_id)
- `PhoneNumber` → `User` (user_id)
- `PhoneNumber` → `Client` (client_id)

#### Use Cases que Dependem de PhoneNumber
- `PhoneNumber\Store` - Cria números por organização
- `PhoneNumber\Update` - Atualiza tokens WhatsApp
- `PhoneNumber\GetAll` - Lista por organização
- `FindOrCreateClient` - Busca organização via phone_number_id

#### Services que Dependem de PhoneNumber
- `WhatsAppService` - Construtor aceita PhoneNumber opcional
- `ChatBotService` - Usa PhoneNumber para identificar organização

### Recomendação: Estratégia Simplificada (Manter PhoneNumber)

#### Justificativa
1. **Zero Breaking Changes**: PhoneNumber permanece como está
2. **Arquitetura Natural**: Uma organização TEM múltiplos números (1:N)
3. **Token por Organização**: Mesmo token de verificação para todos os números da org
4. **Busca Direta**: phone_number_id → PhoneNumber → Organization (simples JOIN)

#### Estratégia Simplificada - 3 Fases

**Fase 1: Preparação e Limpeza (1 dia)**
```php
// 1.1 Resolver conflito de rotas
// Manter apenas: /api/whatsapp/webhook → WhatsAppWebhookController

// 1.2 Centralizar configurações
// config/services.php
'meta' => [
    'whatsapp' => [
        'webhook_verify_token' => env('META_WHATSAPP_WEBHOOK_VERIFY_TOKEN'),
        'webhook_secret' => env('META_WHATSAPP_WEBHOOK_SECRET'),
        'api_base_url' => env('META_WHATSAPP_API_BASE_URL', 'https://graph.facebook.com/v22.0'),
    ]
]

// 1.3 Criar tabela de logs
// whatsapp_webhook_logs para auditoria
```

**Fase 2: Adicionar Token por Organização (1 dia)**
```sql
-- 2.1 Adicionar apenas campo de token de verificação em organizations
ALTER TABLE organizations ADD whatsapp_webhook_verify_token VARCHAR(255) NULL;

-- 2.2 PhoneNumber permanece exatamente como está
-- Nenhuma mudança na estrutura atual
```

**Fase 3: Sistema de Identificação Direto (2 dias)**
```php
// 3.1 OrganizationIdentifier simples
class OrganizationIdentifier
{
    public function identifyByPhoneNumberId(string $phoneNumberId): ?array
    {
        // Busca direta: phone_number_id → PhoneNumber → Organization
        $phoneNumber = PhoneNumber::where('whatsapp_phone_number_id', $phoneNumberId)
                                 ->where('is_active', true)
                                 ->with('organization')
                                 ->first();

        if (!$phoneNumber || !$phoneNumber->organization) {
            return null;
        }

        return [
            'organization' => $phoneNumber->organization,
            'phone_number' => $phoneNumber
        ];
    }
}

// 3.2 WhatsAppService permanece como está
class WhatsAppService
{
    public function __construct(?PhoneNumber $phoneNumber = null)
    {
        // Mantém exatamente a lógica atual
        $this->phoneNumberId = $phoneNumber?->whatsapp_phone_number_id ?? config('whatsapp.phone_number_id');
        $this->businessId = $phoneNumber?->whatsapp_business_id ?? config('whatsapp.business_id');
        $this->token = $phoneNumber?->whatsapp_access_token ?? config('whatsapp.access_token');
    }
}

// 3.3 Webhook Controller
class WhatsAppWebhookController
{
    public function verify(Request $request): JsonResponse
    {
        $token = $request->query('hub_verify_token');

        // Buscar organização que tem esse token
        $organization = Organization::where('whatsapp_webhook_verify_token', $token)
                                  ->where('is_active', true)
                                  ->first();

        if ($organization) {
            return response()->json((int) $request->query('hub_challenge'));
        }

        // Fallback para token global
        if ($token === config('services.meta.whatsapp.webhook_verify_token')) {
            return response()->json((int) $request->query('hub_challenge'));
        }

        return response()->json(['error' => 'Forbidden'], 403);
    }

    public function handle(Request $request): JsonResponse
    {
        // Extrair phone_number_id do payload
        // Buscar PhoneNumber → Organization
        // Rotear para ChatBot ou Campanhas
    }
}
```

### Vantagens da Estratégia Simplificada

#### ✅ Benefícios
1. **Zero Breaking Changes**: PhoneNumber permanece exatamente como está
2. **Arquitetura Natural**: Organization → PhoneNumber (1:N) mantida
3. **Token por Organização**: Mesmo token para todos os números da org
4. **Busca Simples**: phone_number_id → PhoneNumber → Organization (JOIN direto)
5. **Flexibilidade Total**: Múltiplos números por organização naturalmente

#### ⚠️ Considerações
1. **JOIN Necessário**: Busca requer PhoneNumber → Organization
2. **Token Repetido**: Mesmo token pode estar em múltiplas configurações Meta
3. **Configuração**: Cada número precisa ser configurado no Meta com mesmo token

#### 🎯 Resultado Final
- **Webhook Unificado**: Um endpoint, múltiplas organizações
- **Identificação Direta**: phone_number_id → PhoneNumber → Organization
- **Sistema Robusto**: Aproveita estrutura atual
- **Zero Migração**: Nada precisa ser migrado

## Próximos Passos Revisados

### Fase 1: Preparação (Prioridade ALTA)
1. **Resolver Conflito de Rotas**
   - Remover rotas duplicadas
   - Manter apenas WhatsAppWebhookController
   - Testar webhook atual

2. **Centralizar Configurações**
   - Mover configs para `config/services.php`
   - Atualizar variáveis de ambiente
   - Documentar configuração por organização

3. **Criar Sistema de Logs**
   - Migration para `whatsapp_webhook_logs`
   - Implementar logging detalhado
   - Dashboard de monitoramento

### Fase 2: Token por Organização (Prioridade MÉDIA)
1. **Adicionar Campo Token**
   - Migration para `whatsapp_webhook_verify_token` em organizations
   - Manter PhoneNumber exatamente como está
   - Testes de compatibilidade

2. **Implementar Identificação Direta**
   - OrganizationIdentifier simples (PhoneNumber → Organization)
   - Manter WhatsAppService como está
   - Testes unitários completos

3. **Controller Unificado**
   - Verificação por token de organização + fallback global
   - Processar messages e statuses
   - Roteamento por tipo de evento
   - Validação de assinatura

### Fase 3: Configuração Meta (Prioridade BAIXA)
1. **Documentação de Setup**
   - Como configurar múltiplos números com mesmo token
   - Guia de configuração no Meta Business
   - Troubleshooting

2. **Interface de Configuração**
   - Tela para definir token por organização
   - Validação de tokens
   - Testes de conectividade

### Fase 4: Monitoramento (Prioridade CONTÍNUA)
1. **Métricas e Alertas**
   - Performance por organização
   - Taxa de sucesso de webhooks
   - Alertas para falhas

2. **Documentação e Treinamento**
   - Guias de configuração
   - Troubleshooting
   - Best practices
