# Sistema de Webhook Multi-Organizacional WhatsApp

## Visão Geral

Este documento define a arquitetura e implementação do sistema de webhook unificado para múltiplas organizações usando a Meta WhatsApp Cloud API.

## Contexto Atual

### Problema Identificado
- Atualmente temos dois controllers de webhook (`WebhookController` e `WhatsAppWebhookController`)
- Sistema não está preparado para multi-tenant adequadamente
- Falta mapeamento entre `phone_number_id` e organizações
- Token de verificação único para todas as organizações

### Meta WhatsApp Cloud API - Como Funciona
- **Um webhook único** por aplicação Meta (não por organização)
- Payload sempre contém `phone_number_id` e `whatsapp_business_account_id`
- Diferentes tipos de eventos no mesmo endpoint:
  - `messages`: Mensagens recebidas de clientes
  - `statuses`: Status de mensagens enviadas (entregue, lida, falhou)
  - Outros eventos futuros

## Arquitetura Proposta

### 1. Webhook Unificado

```
Meta WhatsApp Cloud API
        ↓
Webhook Único (/api/whatsapp/webhook)
        ↓
Identificação da Organização (phone_number_id)
        ↓
Roteamento por Tipo de Evento
        ↓
┌─────────────────┬─────────────────┐
│   ChatBot       │   Campanhas     │
│  (messages)     │  (statuses)     │
└─────────────────┴─────────────────┘
```

### 2. Estrutura de Dados

#### Tabela: organizations
```sql
-- Campos existentes +
whatsapp_phone_number_id VARCHAR(255) NULL,
whatsapp_business_account_id VARCHAR(255) NULL,
whatsapp_access_token TEXT NULL,
whatsapp_webhook_verify_token VARCHAR(255) NULL, -- Token específico por org (opcional)
```

#### Tabela: whatsapp_webhook_logs
```sql
id BIGINT PRIMARY KEY,
organization_id BIGINT,
phone_number_id VARCHAR(255),
business_account_id VARCHAR(255),
event_type ENUM('message', 'status', 'other'),
webhook_payload JSON,
processed_at TIMESTAMP,
processing_status ENUM('pending', 'success', 'failed'),
error_message TEXT NULL,
created_at TIMESTAMP,
updated_at TIMESTAMP
```

### 3. Fluxo de Processamento

#### 3.1 Verificação do Webhook (GET)
```php
// Único token de verificação global
if ($mode === 'subscribe' && $token === config('services.meta.webhook_verify_token')) {
    return response()->json((int) $challenge);
}
```

#### 3.2 Processamento de Eventos (POST)
```php
1. Receber payload do webhook
2. Extrair phone_number_id do payload
3. Buscar organização pelo phone_number_id
4. Validar se organização está ativa
5. Determinar tipo de evento (messages vs statuses)
6. Rotear para módulo apropriado
7. Registrar log do processamento
```

## Implementação Detalhada

### 1. Controller Unificado

```php
class WhatsAppWebhookController extends Controller
{
    public function verify(Request $request): JsonResponse
    {
        // Verificação única global
    }

    public function handle(Request $request): JsonResponse
    {
        // 1. Validar estrutura do payload
        // 2. Processar cada entry
        // 3. Para cada change, identificar organização
        // 4. Rotear por tipo de evento
    }

    private function identifyOrganization(array $changeValue): ?Organization
    {
        // Buscar por phone_number_id ou business_account_id
    }

    private function routeEvent(string $eventType, array $data, Organization $org): array
    {
        // Rotear para ChatBot ou Campanhas
    }
}
```

### 2. Use Cases

#### ProcessWebhookMessage (ChatBot)
```php
class ProcessWebhookMessage
{
    public function perform(array $webhookData, Organization $organization): array
    {
        // Processar mensagens recebidas
        // Usar access_token da organização
    }
}
```

#### ProcessWebhookStatus (Campanhas)
```php
class ProcessWebhookStatus
{
    public function perform(array $webhookData, Organization $organization): array
    {
        // Processar status de mensagens enviadas
        // Atualizar logs de campanha
    }
}
```

### 3. Identificação de Organização

```php
class OrganizationIdentifier
{
    public function identifyByPhoneNumberId(string $phoneNumberId): ?Organization
    {
        return Organization::where('whatsapp_phone_number_id', $phoneNumberId)
                          ->where('is_active', true)
                          ->first();
    }

    public function identifyByBusinessAccountId(string $businessAccountId): ?Organization
    {
        return Organization::where('whatsapp_business_account_id', $businessAccountId)
                          ->where('is_active', true)
                          ->first();
    }
}
```

## Estrutura de Payload

### Mensagem Recebida
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "**********"
            },
            "contacts": [
              {
                "profile": {"name": "João Silva"},
                "wa_id": "*************"
              }
            ],
            "messages": [
              {
                "from": "*************",
                "id": "wamid.HBgLM...",
                "timestamp": "**********",
                "text": {"body": "Oi, tudo bem?"},
                "type": "text"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

### Status de Mensagem
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "**********"
            },
            "statuses": [
              {
                "id": "wamid.HBgLM...",
                "status": "delivered",
                "timestamp": "**********",
                "recipient_id": "*************"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

## Segurança

### 1. Validação de Assinatura
```php
// Validar X-Hub-Signature-256 do Meta
private function validateWebhookSignature(Request $request): bool
{
    $signature = $request->header('X-Hub-Signature-256');
    $payload = $request->getContent();
    $secret = config('services.meta.webhook_secret');
    
    $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
    
    return hash_equals($expectedSignature, $signature);
}
```

### 2. Rate Limiting
```php
// Aplicar rate limiting por organização
Route::middleware(['throttle:whatsapp-webhook'])->group(function () {
    Route::post('/webhook', [WhatsAppWebhookController::class, 'handle']);
});
```

## Análise da Estrutura Atual

### Problemas Identificados

#### 1. Rotas Duplicadas
```php
// routes/api.php - CONFLITO
Route::get("/whatsapp/webhook", [WhatsAppWebhookController::class, "verify"]);
Route::post("/whatsapp/webhook", [WhatsAppWebhookController::class, "receiveMessage"]);

Route::prefix('whatsapp/webhook')->group(function () {
    Route::get('/', [WebhookController::class, 'verify']);
    Route::post('/', [WebhookController::class, 'handle']);
});
```

#### 2. Configurações Fragmentadas
- `config/whatsapp.php` - Configurações básicas
- `config/services.php` - Sem seção Meta/WhatsApp
- Variáveis de ambiente espalhadas em múltiplos arquivos

#### 3. Dados WhatsApp Distribuídos
- **PhoneNumber**: Contém `whatsapp_phone_number_id`, `whatsapp_business_id`, `whatsapp_access_token`
- **Organization**: Não possui campos WhatsApp diretos
- **Problema**: Mapeamento phone_number_id → organization requer JOIN

#### 4. Controllers Conflitantes
- `WhatsAppWebhookController`: Foca em ChatBot
- `WebhookController`: Usa ProcessWebhookEntry use case
- **Problema**: Lógica duplicada e inconsistente

### Dependências Atuais

#### Models Relacionados
- `Organization` - Cliente principal
- `PhoneNumber` - Contém dados WhatsApp
- `WhatsAppMessage` - Mensagens processadas
- `WhatsAppWebhookEntry` - Logs de webhook

#### Use Cases Existentes
- `ProcessWebhookEntry` - Processamento genérico
- `FindOrCreateClient` - Criação de clientes
- `FindOrCreateConversation` - Gestão de conversas
- `ProcessFlowStep` - Lógica do ChatBot

#### Services
- `WhatsAppService` - API do WhatsApp
- `ChatBotService` - Processamento de fluxos

### Estratégia de Migração

#### Fase 1: Consolidação
1. **Unificar Controllers**: Manter apenas um webhook controller
2. **Centralizar Configurações**: Mover tudo para `config/services.php`
3. **Limpar Rotas**: Remover duplicatas

#### Fase 2: Migração de Dados
1. **Adicionar Campos**: WhatsApp fields na tabela `organizations`
2. **Migrar Dados**: PhoneNumber → Organization
3. **Manter Compatibilidade**: Período de transição

#### Fase 3: Refatoração
1. **Atualizar Use Cases**: Usar Organization em vez de PhoneNumber
2. **Simplificar Identificação**: Busca direta por phone_number_id
3. **Implementar Logs**: Sistema de auditoria completo

## Próximos Passos

1. **Migração de Dados**
   - Adicionar campos WhatsApp na tabela organizations
   - Migrar dados existentes do PhoneNumber para Organization

2. **Implementação**
   - Criar controller unificado
   - Implementar use cases de roteamento
   - Criar sistema de logs

3. **Testes**
   - Testes unitários para identificação de organização
   - Testes de integração para fluxo completo
   - Testes de webhook com payloads reais

4. **Monitoramento**
   - Logs detalhados de processamento
   - Métricas de performance por organização
   - Alertas para falhas de processamento
